from shapely.geometry import Polygon
from typing import List, Dict, <PERSON><PERSON>

def get_reward(state: Dict, action_dict: Dict) -> <PERSON><PERSON>[float, bool]:
    """
    Returns reward and truncation flag.
    Reward = 1 if valid placement, else 0.
    """
    container = state['container']
    obstacles = state['obstacles']
    spots = state['spots']
    spot = action_dict['spot']

    # Check if valid
    if not container.contains(spot):
        return 0.0, True
    if any(spot.intersects(obs) for obs in obstacles):
        return 0.0, True
    if any(spot.intersects(existing) for existing in spots):
        return 0.0, True

    return 1.0, False
