@ECHO OFF
@SET PYTHONIOENCODING=utf-8
@SET PYTHONUTF8=1
@FOR /F "tokens=2 delims=:." %%A in ('chcp') do for %%B in (%%A) do set "_CONDA_OLD_CHCP=%%B"
@chcp 65001 > NUL
@CALL "C:\Users\<USER>\miniconda3\condabin\conda.bat" activate "C:\Users\<USER>\OneDrive\Desktop\personal\consigli\conda_env"
@IF %ERRORLEVEL% NEQ 0 EXIT /b %ERRORLEVEL%
@C:\Users\<USER>\OneDrive\Desktop\personal\consigli\conda_env\python.exe -m pip install -U -r C:\Users\<USER>\OneDrive\Desktop\personal\consigli\condaenv.q97sjz18.requirements.txt --exists-action=b
@IF %ERRORLEVEL% NEQ 0 EXIT /b %ERRORLEVEL%
@chcp %_CONDA_OLD_CHCP%>NUL
