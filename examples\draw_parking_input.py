from __future__ import annotations

import json
from typing import Any, List, Optional, Sequence, Tuple

import matplotlib.pyplot as plt
import numpy as np


def _is_point(obj: Any) -> bool:
    """Return True if obj looks like a 2D/3D point in list/tuple or dict form."""
    if isinstance(obj, (list, tuple)) and len(obj) >= 2:
        return isinstance(obj[0], (int, float)) and isinstance(obj[1], (int, float))
    if isinstance(obj, dict):
        has_xy_lower = "x" in obj and "y" in obj
        has_xy_upper = "X" in obj and "Y" in obj
        return has_xy_lower or has_xy_upper
    return False


def _is_points_list(obj: Any) -> bool:
    return isinstance(obj, list) and len(obj) > 0 and _is_point(obj[0])


def _is_polygons_list(obj: Any) -> bool:
    return isinstance(obj, list) and len(obj) > 0 and _is_points_list(obj[0])


essential_float = (int, float)


def _to_xy(points: Sequence[Any]) -> List[Tuple[float, float]]:
    """Normalize a sequence of raw points to a list of (x, y) floats."""
    xy: List[Tuple[float, float]] = []
    for p in points:
        if isinstance(p, (list, tuple)):
            if len(p) < 2:
                continue
            x_val = float(p[0])
            y_val = float(p[1])
            xy.append((x_val, y_val))
        elif isinstance(p, dict):
            if "x" in p and "y" in p:
                xy.append((float(p["x"]), float(p["y"])))
            elif "X" in p and "Y" in p:
                xy.append((float(p["X"]), float(p["Y"])))
            # Ignore dicts that do not expose x/y
        # Ignore anything else silently
    return xy


def _ensure_closed(points_xy: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    if not points_xy:
        return points_xy
    if points_xy[0] != points_xy[-1]:
        return points_xy + [points_xy[0]]
    return points_xy


def _extract_polygons(raw: Any) -> List[List[Tuple[float, float]]]:
    """Extract one or more polygons from raw structure.

    Accepts either:
    - single polygon: list of points
    - multi polygon: list of list-of-points
    Points may be [x, y], [x, y, z], or dicts with x/y (upper/lower case).
    """
    if _is_points_list(raw):
        return [_to_xy(raw)]
    if _is_polygons_list(raw):
        return [_to_xy(poly) for poly in raw]
    raise ValueError(
        "Unsupported polygon structure. Expect list-of-points or list-of-list-of-points."
    )


def draw_parking_input(
    json_file_path: str,
    show: bool = True,
    save_path: Optional[str] = None,
    figsize: Tuple[int, int] = (12, 8),
) -> None:
    """Draw container polygon and obstacle polygons from the provided JSON file.

    Expects keys:
    - GlobalRoomInfo.ContainerPoints: polygon or list of polygons
    - RoomElementsInfo: list where each element may have SurfacePoints as polygon or list of polygons
    - GlobalRoomInfo.RoomEntrances: list where each entrance has SurfacePoints defining the entrance area/segment
    """
    with open(json_file_path, "r") as f:
        data = json.load(f)

    # Container(s)
    container_raw = data["GlobalRoomInfo"]["ContainerPoints"]
    try:
        container_polygons = _extract_polygons(container_raw)
    except ValueError:
        # Fallback: if nested e.g. [[[x,y,z], ...]] choose first polygon
        if isinstance(container_raw, list) and container_raw and _is_points_list(container_raw[0]):
            container_polygons = [_to_xy(container_raw[0])]
        else:
            raise

    # Obstacles
    obstacles_raw_list = data.get("RoomElementsInfo", [])
    obstacle_polygons: List[List[Tuple[float, float]]] = []
    for elem in obstacles_raw_list:
        surface = (
            elem.get("SurfacePoints")
            or elem.get("surfacePoints")
            or elem.get("Points")
            or elem.get("points")
        )
        if surface is None:
            continue
        try:
            polys = _extract_polygons(surface)
        except ValueError:
            if _is_points_list(surface):
                polys = [_to_xy(surface)]
            elif isinstance(surface, list) and surface and _is_points_list(surface[0]):
                polys = [_to_xy(surface[0])]
            else:
                continue
        obstacle_polygons.extend(polys)

    # Entrances (as segments/polylines)
    entrances_raw_list = data.get("GlobalRoomInfo", {}).get("RoomEntrances", [])
    entrance_polylines: List[List[Tuple[float, float]]] = []
    for entrance in entrances_raw_list:
        surf = entrance.get("SurfacePoints") or entrance.get("surfacePoints")
        if surf is None:
            continue
        try:
            polys = _extract_polygons(surf)
        except ValueError:
            if _is_points_list(surf):
                polys = [_to_xy(surf)]
            elif isinstance(surf, list) and surf and _is_points_list(surf[0]):
                polys = [_to_xy(surf[0])]
            else:
                continue
        # Treat each as a polyline; do not fill
        entrance_polylines.extend(polys)

    fig, ax = plt.subplots(figsize=figsize)

    # Plot container(s)
    for i, poly in enumerate(container_polygons):
        xy = _ensure_closed(poly)
        if len(xy) < 4:  # need at least 3 unique points + closing point
            continue
        xs = [p[0] for p in xy]
        ys = [p[1] for p in xy]
        ax.plot(xs, ys, color="black", linewidth=2, label="Container" if i == 0 else "")

    # Plot obstacles
    for i, poly in enumerate(obstacle_polygons):
        xy = _ensure_closed(poly)
        if len(xy) < 4:
            continue
        xs = [p[0] for p in xy]
        ys = [p[1] for p in xy]
        ax.fill(xs, ys, color="red", alpha=0.5, label="Obstacle" if i == 0 else "")

    # Plot entrances as segments (polylines)
    for i, poly in enumerate(entrance_polylines):
        if len(poly) < 2:
            continue
        xs = [p[0] for p in poly]
        ys = [p[1] for p in poly]
        # Draw as thicker dashed lines; do not close or fill
        ax.plot(
            xs, ys, color="blue", linewidth=3, linestyle="--", label="Entrance" if i == 0 else ""
        )

    ax.set_aspect("equal", adjustable="box")

    # Handle legend without duplicates
    handles, labels = ax.get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    if by_label:
        ax.legend(by_label.values(), by_label.keys(), loc="best")

    ax.set_title("Parking Input: Container, Obstacles, Entrances")

    if save_path:
        plt.savefig(save_path, bbox_inches="tight", dpi=300)
    if show:
        plt.show()
    else:
        plt.close(fig)


if __name__ == "__main__":
    path_file = "examples/parkings_slippen.json"
    # Quick manual test: update `path_file` if needed
    try:
        draw_parking_input(path_file, show=True)
    except Exception as e:
        print(f"Failed to draw input: {e}")
