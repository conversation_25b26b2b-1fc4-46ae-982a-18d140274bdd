"""
Enhanced parking visualization with Voronoi diagrams.

This module extends the original draw_parking_input functionality to include
Voronoi diagrams between obstacle centerpoints using scipy.
"""

from __future__ import annotations

import json
from typing import Any, List, Optional, Sequence, Tuple

import matplotlib.pyplot as plt
import numpy as np
from scipy.spatial import Voronoi, voronoi_plot_2d


def _is_point(obj: Any) -> bool:
    """Return True if obj looks like a 2D/3D point in list/tuple or dict form."""
    if isinstance(obj, (list, tuple)) and len(obj) >= 2:
        return isinstance(obj[0], (int, float)) and isinstance(obj[1], (int, float))
    if isinstance(obj, dict):
        has_xy_lower = "x" in obj and "y" in obj
        has_xy_upper = "X" in obj and "Y" in obj
        return has_xy_lower or has_xy_upper
    return False


def _is_points_list(obj: Any) -> bool:
    return isinstance(obj, list) and len(obj) > 0 and _is_point(obj[0])


def _is_polygons_list(obj: Any) -> bool:
    return isinstance(obj, list) and len(obj) > 0 and _is_points_list(obj[0])


essential_float = (int, float)


def _to_xy(points: Sequence[Any]) -> List[Tuple[float, float]]:
    """Normalize a sequence of raw points to a list of (x, y) floats."""
    xy: List[Tuple[float, float]] = []
    for p in points:
        if isinstance(p, (list, tuple)):
            if len(p) < 2:
                continue
            x_val = float(p[0])
            y_val = float(p[1])
            xy.append((x_val, y_val))
        elif isinstance(p, dict):
            if "x" in p and "y" in p:
                xy.append((float(p["x"]), float(p["y"])))
            elif "X" in p and "Y" in p:
                xy.append((float(p["X"]), float(p["Y"])))
            # Ignore dicts that do not expose x/y
        # Ignore anything else silently
    return xy


def _ensure_closed(points_xy: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    if not points_xy:
        return points_xy
    if points_xy[0] != points_xy[-1]:
        return points_xy + [points_xy[0]]
    return points_xy


def _extract_polygons(raw: Any) -> List[List[Tuple[float, float]]]:
    """Extract one or more polygons from raw structure.

    Accepts either:
    - single polygon: list of points
    - multi polygon: list of list-of-points
    Points may be [x, y], [x, y, z], or dicts with x/y (upper/lower case).
    """
    if _is_points_list(raw):
        return [_to_xy(raw)]
    if _is_polygons_list(raw):
        return [_to_xy(poly) for poly in raw]
    raise ValueError(
        "Unsupported polygon structure. Expect list-of-points or list-of-list-of-points."
    )


def _calculate_polygon_centroid(points: List[Tuple[float, float]]) -> Tuple[float, float]:
    """Calculate the centroid of a polygon."""
    if not points:
        return (0.0, 0.0)

    # Remove duplicate closing point if present
    if len(points) > 1 and points[0] == points[-1]:
        points = points[:-1]

    if len(points) == 1:
        return points[0]

    # Calculate centroid using the shoelace formula
    n = len(points)
    cx = sum(p[0] for p in points) / n
    cy = sum(p[1] for p in points) / n

    return (cx, cy)


def _clip_voronoi_to_bounds(vor: Voronoi, bounds: Tuple[float, float, float, float]) -> List[List[Tuple[float, float]]]:
    """Clip Voronoi diagram to specified bounds and return finite regions."""
    x_min, y_min, x_max, y_max = bounds
    clipped_regions = []

    for region in vor.regions:
        if not region or -1 in region:  # Skip infinite regions
            continue

        # Get vertices of this region
        vertices = [vor.vertices[i] for i in region]

        # Simple clipping: only keep vertices within bounds
        clipped_vertices = []
        for x, y in vertices:
            if x_min <= x <= x_max and y_min <= y <= y_max:
                clipped_vertices.append((x, y))

        if len(clipped_vertices) >= 3:  # Need at least 3 points for a polygon
            clipped_regions.append(clipped_vertices)

    return clipped_regions


def _clip_voronoi_to_container(vor: Voronoi, container_polygon: List[Tuple[float, float]]) -> List[List[Tuple[float, float]]]:
    """Clip Voronoi diagram to container polygon and return finite regions."""
    try:
        from shapely.geometry import Polygon, Point
        from shapely.ops import unary_union

        # Create container polygon
        container_shape = Polygon(container_polygon)
        clipped_regions = []

        for region in vor.regions:
            if not region or -1 in region:  # Skip infinite regions
                continue

            # Get vertices of this region
            vertices = [vor.vertices[i] for i in region]

            if len(vertices) < 3:
                continue

            try:
                # Create Voronoi cell polygon
                cell_polygon = Polygon(vertices)

                # Clip to container
                clipped = cell_polygon.intersection(container_shape)

                # Extract coordinates if valid
                if hasattr(clipped, 'exterior') and clipped.area > 0:
                    coords = list(clipped.exterior.coords[:-1])  # Remove duplicate closing point
                    if len(coords) >= 3:
                        clipped_regions.append(coords)

            except Exception:
                # Fallback to simple bounds clipping
                continue

        return clipped_regions

    except ImportError:
        # Fallback to bounds clipping if shapely not available
        bounds = (min(p[0] for p in container_polygon),
                 min(p[1] for p in container_polygon),
                 max(p[0] for p in container_polygon),
                 max(p[1] for p in container_polygon))
        return _clip_voronoi_to_bounds(vor, bounds)


def draw_parking_with_voronoi(
    json_file_path: str,
    show: bool = True,
    save_path: Optional[str] = None,
    figsize: Tuple[int, int] = (14, 10),
    show_voronoi: bool = True,
    voronoi_alpha: float = 0.3,
) -> None:
    """Draw container polygon, obstacles, entrances, and Voronoi diagram of obstacle centers.

    Expects keys:
    - GlobalRoomInfo.ContainerPoints: polygon or list of polygons
    - RoomElementsInfo: list where each element may have SurfacePoints as polygon or list of polygons
    - GlobalRoomInfo.RoomEntrances: list where each entrance has SurfacePoints defining the entrance area/segment

    Args:
        json_file_path: Path to the JSON file containing parking data
        show: Whether to display the plot
        save_path: Optional path to save the figure
        figsize: Figure size as (width, height)
        show_voronoi: Whether to show the Voronoi diagram
        voronoi_alpha: Transparency of Voronoi regions
    """
    with open(json_file_path, "r") as f:
        data = json.load(f)

    # Container(s)
    container_raw = data["GlobalRoomInfo"]["ContainerPoints"]
    try:
        container_polygons = _extract_polygons(container_raw)
    except ValueError:
        # Fallback: if nested e.g. [[[x,y,z], ...]] choose first polygon
        if isinstance(container_raw, list) and container_raw and _is_points_list(container_raw[0]):
            container_polygons = [_to_xy(container_raw[0])]
        else:
            raise

    # Obstacles and their centroids
    obstacles_raw_list = data.get("RoomElementsInfo", [])
    obstacle_polygons: List[List[Tuple[float, float]]] = []
    obstacle_centroids: List[Tuple[float, float]] = []

    for elem in obstacles_raw_list:
        surface = (
            elem.get("SurfacePoints")
            or elem.get("surfacePoints")
            or elem.get("Points")
            or elem.get("points")
        )
        if surface is None:
            continue
        try:
            polys = _extract_polygons(surface)
        except ValueError:
            if _is_points_list(surface):
                polys = [_to_xy(surface)]
            elif isinstance(surface, list) and surface and _is_points_list(surface[0]):
                polys = [_to_xy(surface[0])]
            else:
                continue

        for poly in polys:
            obstacle_polygons.append(poly)
            centroid = _calculate_polygon_centroid(poly)
            obstacle_centroids.append(centroid)

    # Entrances (as segments/polylines)
    entrances_raw_list = data.get("GlobalRoomInfo", {}).get("RoomEntrances", [])
    entrance_polylines: List[List[Tuple[float, float]]] = []
    for entrance in entrances_raw_list:
        surf = entrance.get("SurfacePoints") or entrance.get("surfacePoints")
        if surf is None:
            continue
        try:
            polys = _extract_polygons(surf)
        except ValueError:
            if _is_points_list(surf):
                polys = [_to_xy(surf)]
            elif isinstance(surf, list) and surf and _is_points_list(surf[0]):
                polys = [_to_xy(surf[0])]
            else:
                continue
        # Treat each as a polyline; do not fill
        entrance_polylines.extend(polys)

    fig, ax = plt.subplots(figsize=figsize)

    # Calculate bounds for clipping Voronoi diagram
    if container_polygons:
        all_x = [p[0] for poly in container_polygons for p in poly]
        all_y = [p[1] for poly in container_polygons for p in poly]
        x_min, x_max = min(all_x), max(all_x)
        y_min, y_max = min(all_y), max(all_y)
        bounds = (x_min, y_min, x_max, y_max)
    else:
        bounds = (0, 0, 100, 100)  # Default bounds

    # Plot Voronoi diagram if requested and we have enough points
    if show_voronoi and len(obstacle_centroids) >= 3:
        try:
            # Combine obstacle centroids with container vertices for Voronoi
            all_voronoi_points = list(obstacle_centroids)

            # Add container polygon vertices to the Voronoi points
            if container_polygons:
                for container_poly in container_polygons:
                    # Remove duplicate closing point if present
                    container_points = container_poly[:-1] if container_poly and container_poly[0] == container_poly[-1] else container_poly
                    all_voronoi_points.extend(container_points)

            points = np.array(all_voronoi_points)
            vor = Voronoi(points)

            # Plot Voronoi regions with container-aware clipping
            if container_polygons:
                # Use the first container polygon for clipping
                clipped_regions = _clip_voronoi_to_container(vor, container_polygons[0])
            else:
                clipped_regions = _clip_voronoi_to_bounds(vor, bounds)
            for i, region in enumerate(clipped_regions):
                if len(region) >= 3:
                    region_x = [p[0] for p in region]
                    region_y = [p[1] for p in region]
                    ax.fill(region_x, region_y, alpha=voronoi_alpha,
                           color=plt.cm.Set3(i % 12),
                           label='Voronoi regions' if i == 0 else '')

            # Plot Voronoi edges
            for simplex in vor.ridge_vertices:
                if -1 not in simplex:  # Skip infinite edges
                    points_on_edge = vor.vertices[simplex]
                    # Only plot edges within bounds
                    if all(bounds[0] <= p[0] <= bounds[2] and bounds[1] <= p[1] <= bounds[3]
                           for p in points_on_edge):
                        ax.plot(points_on_edge[:, 0], points_on_edge[:, 1], 'k--', alpha=0.5, linewidth=0.5)

        except Exception as e:
            print(f"Warning: Could not generate Voronoi diagram: {e}")

    # Plot container(s)
    for i, poly in enumerate(container_polygons):
        xy = _ensure_closed(poly)
        if len(xy) < 4:  # need at least 3 unique points + closing point
            continue
        xs = [p[0] for p in xy]
        ys = [p[1] for p in xy]
        ax.plot(xs, ys, color="black", linewidth=2, label="Container" if i == 0 else "")

    # Plot obstacles
    for i, poly in enumerate(obstacle_polygons):
        xy = _ensure_closed(poly)
        if len(xy) < 4:
            continue
        xs = [p[0] for p in xy]
        ys = [p[1] for p in xy]
        ax.fill(xs, ys, color="red", alpha=0.7, label="Obstacles" if i == 0 else "")

    # Plot obstacle centroids
    if obstacle_centroids:
        centroids_x = [c[0] for c in obstacle_centroids]
        centroids_y = [c[1] for c in obstacle_centroids]
        ax.plot(centroids_x, centroids_y, 'ro', markersize=6, label="Obstacle centers")

    # Plot container vertices used in Voronoi
    if show_voronoi and container_polygons:
        container_vertices = []
        for container_poly in container_polygons:
            # Remove duplicate closing point if present
            container_points = container_poly[:-1] if container_poly and container_poly[0] == container_poly[-1] else container_poly
            container_vertices.extend(container_points)

        if container_vertices:
            vertices_x = [v[0] for v in container_vertices]
            vertices_y = [v[1] for v in container_vertices]
            ax.plot(vertices_x, vertices_y, 'bs', markersize=4, label="Container vertices")

    # Plot entrances as segments (polylines)
    for i, poly in enumerate(entrance_polylines):
        if len(poly) < 2:
            continue
        xs = [p[0] for p in poly]
        ys = [p[1] for p in poly]
        # Draw as thicker dashed lines; do not close or fill
        ax.plot(
            xs, ys, color="blue", linewidth=3, linestyle="--", label="Entrances" if i == 0 else ""
        )

    ax.set_aspect("equal", adjustable="box")

    # Handle legend without duplicates
    handles, labels = ax.get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    if by_label:
        ax.legend(by_label.values(), by_label.keys(), loc="best")

    title = "Parking Input: Container, Obstacles, Entrances"
    if show_voronoi and len(obstacle_centroids) >= 3:
        title += " with Voronoi Diagram"
    ax.set_title(title)
    ax.grid(True, alpha=0.3)

    if save_path:
        plt.savefig(save_path, bbox_inches="tight", dpi=300)
    if show:
        plt.show()
    else:
        plt.close(fig)


if __name__ == "__main__":
    path_file = "examples/parkings_slippen.json"
    # Quick manual test: update `path_file` if needed
    try:
        draw_parking_with_voronoi(path_file, show=True, show_voronoi=True)
    except Exception as e:
        print(f"Failed to draw input with Voronoi: {e}")