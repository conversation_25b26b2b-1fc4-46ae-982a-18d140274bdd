import gymnasium as gym
import numpy as np
import shapely
from collections import Tuple, List

class ParkingEnv(gym.Env):
    def __init__(self,
        container: shapely.Polygon,
        obstacles: List[shapely.Polygon],
        entrances: List[shapely.Point],
        std_measurements: List[float]
    ):
        self.CONTAINER = container
        self.OBSTACLES = obstacles
        self.ENTRANCES = entrances
        self.STD_MEASUREMENTS = std_measurements ## width parking spot, length parking spot, width lane


    def step(self, action):
        pass
    def get_reward(self, state, action):
        pass
    def reset(self, seed=None, options=None):
        pass