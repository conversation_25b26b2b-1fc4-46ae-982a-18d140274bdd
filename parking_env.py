import gymnasium as gym
from gymnasium import spaces
import numpy as np
from shapely.geometry import <PERSON><PERSON><PERSON>, Point
from shapely.affinity import rotate, translate
from typing import List, Dict, Tuple
from reward import compute_reward_and_done

class ParkingEnv(gym.Env):
    def __init__(self,
        container: Polygon,
        obstacles: List[Polygon],
        entrances: List[Point],
        std_measurements: List[float],  # [width, length, lane_width]
        max_steps: int = 100
    ):
        super().__init__()
        self.CONTAINER = container
        self.OBSTACLES = obstacles
        self.ENTRANCES = entrances
        self.STD_MEASUREMENTS = std_measurements
        self.max_steps = max_steps

        # Action: [x, y, rotation_degrees, spot_type]
        # x, y normalized [0,1] inside container bbox for simplicity
        # rotation in degrees [0, 360)
        # spot_type: 0 = normal, 1 = handicap
        self.action_space = spaces.Box(low=np.array([0, 0, 0, 0]),
                                        high=np.array([1, 1, 360, 1]),
                                        dtype=np.float32)
        # Observation: number of placed spots normalized by max_steps
        self.observation_space = spaces.Box(low=0, high=1, shape=(1,), dtype=np.float32)

        self.state = None
        self.steps_taken = 0

    def reset(self, seed=None, options=None):
        self.state = {
            'container': self.CONTAINER,
            'obstacles': self.OBSTACLES,
            'layout': [],
            'entrances': self.ENTRANCES,
        }
        self.steps_taken = 0
        # Return initial observation (number of placed spots / max_steps)
        obs = np.array([0.0], dtype=np.float32)
        return obs, {}

    def step(self, action):
        self.steps_taken += 1

        # Convert normalized action x,y to real coords inside container bbox
        minx, miny, maxx, maxy = self.CONTAINER.bounds
        x = minx + action[0] * (maxx - minx)
        y = miny + action[1] * (maxy - miny)
        rot = action[2] % 360
        spot_type = int(round(action[3]))

        # Get spot dimensions based on type
        if spot_type == 0:
            length = self.STD_MEASUREMENTS[1]  # length
            width = self.STD_MEASUREMENTS[0]   # width
        else:
            length = 6.0
            width = 4.5

        # Create spot polygon, rectangle at origin
        spot = Polygon([(0, 0), (length, 0), (length, width), (0, width)])
        spot = rotate(spot, rot, origin=(0, 0))
        spot = translate(spot, xoff=x, yoff=y)

        last_action = {'spot': spot}

        reward, done = compute_reward_and_done(self.state, last_action)

        if not done:
            self.state['layout'].append(spot)

        # Observation: ratio of placed spots to max_steps
        obs = np.array([len(self.state['layout']) / self.max_steps], dtype=np.float32)

        if self.steps_taken >= self.max_steps:
            done = True

        return obs, reward, done, False, {}
