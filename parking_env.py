import gymnasium as gym
from gymnasium import spaces
import numpy as np
from shapely.geometry import <PERSON><PERSON><PERSON>, Point
from shapely.affinity import rotate, translate
from typing import List, Dict, Tuple
from reward import get_reward

class ParkingEnv(gym.Env):
    def __init__(self,
        container: Polygon,
        obstacles: List[Polygon],
        entrances: List[Point],
        std_measurements: List[float],  # [width, length, lane_width]
        max_steps: int = 100
    ):
        self.CONTAINER = container
        self.OBSTACLES = obstacles
        self.ENTRANCES = entrances
        self.STD_MEASUREMENTS = std_measurements
        self.max_steps = max_steps
        self.action_space = None
        self.observation_space = None
        self.dtype = np.float32

    def reset(self, seed=None, options=None):
        self.state = {
            'container': self.CONTAINER,
            'obstacles': self.OBSTACLES,
            'layout': [],
            'entrances': self.ENTRANCES,
        }
        self.time = 0
        obs = np.array([0.0], dtype=np.float32)
        return obs, {}

    def step(self, action):
        self.time += 1
        get_reward(self.state, action)
        return np.array([0.0], dtype=np.float32), 0.0, False, False, {}

