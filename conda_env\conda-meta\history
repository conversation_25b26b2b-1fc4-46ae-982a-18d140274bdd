==> 2025-08-08 16:07:12 <==
# cmd: C:\Users\<USER>\miniconda3\Scripts\conda-script.py env create --prefix ./conda_env -f conda_env.yml
# conda version: 25.1.1
+conda-forge/noarch::ca-certificates-2025.8.3-h4c7d964_0
+conda-forge/noarch::pip-25.2-pyh8b19718_0
+conda-forge/noarch::setuptools-80.9.0-pyhff2d567_0
+conda-forge/noarch::tzdata-2025b-h78e105d_0
+conda-forge/noarch::wheel-0.45.1-pyhd8ed1ab_1
+conda-forge/win-64::bzip2-1.0.8-h2466b09_7
+conda-forge/win-64::libexpat-2.7.1-hac47afa_0
+conda-forge/win-64::libffi-3.4.6-h537db12_1
+conda-forge/win-64::liblzma-5.8.1-h2466b09_2
+conda-forge/win-64::libsqlite-3.50.4-hf5d6505_0
+conda-forge/win-64::libzlib-1.3.1-h2466b09_2
+conda-forge/win-64::openssl-3.5.2-h725018a_0
+conda-forge/win-64::python-3.10.18-h8c5b53a_0_cpython
+conda-forge/win-64::tk-8.6.13-h2c6b04d_2
+conda-forge/win-64::ucrt-10.0.22621.0-h57928b3_1
+conda-forge/win-64::vc-14.3-h41ae7f8_31
+conda-forge/win-64::vc14_runtime-14.44.35208-h818238b_31
+conda-forge/win-64::vcomp14-14.44.35208-h818238b_31
# update specs: ['pip', 'python=3.10']
==> 2025-08-08 18:39:19 <==
# cmd: C:\Users\<USER>\miniconda3\Scripts\conda-script.py env update --prefix ./conda_env --file conda_env.yml --prune
# conda version: 25.1.1
# update specs: ['python=3.10', 'pip']
